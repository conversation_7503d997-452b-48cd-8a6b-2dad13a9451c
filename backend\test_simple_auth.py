#!/usr/bin/env python3
"""
Test script for simple authentication system
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.database import init_database, get_db, User
from app.simple_auth import (
    simple_hash_password, 
    verify_simple_password, 
    authenticate_user, 
    create_user_session
)
from sqlalchemy.orm import Session

def test_simple_auth_system():
    """Test the simple authentication system"""
    print("Testing simple authentication system...")
    
    # Initialize database
    print("1. Initializing database...")
    init_database()
    print("   ✓ Database initialized")
    
    # Get database session
    db_gen = get_db()
    db: Session = next(db_gen)
    
    try:
        # Test password hashing
        print("2. Testing password hashing...")
        test_password = "testpassword123"
        hashed = simple_hash_password(test_password)
        print(f"   ✓ Password hashed: {hashed[:20]}...")
        
        # Test password verification
        print("3. Testing password verification...")
        if verify_simple_password(test_password, hashed):
            print("   ✓ Password verification successful")
        else:
            print("   ✗ Password verification failed")
            return False
        
        # Test wrong password
        if not verify_simple_password("wrongpassword", hashed):
            print("   ✓ Correctly rejected wrong password")
        else:
            print("   ✗ Incorrectly accepted wrong password")
            return False
        
        # Create a test user
        print("4. Creating test user...")
        test_username = "testuser"
        test_email = "<EMAIL>"
        
        # Check if user already exists
        existing_user = db.query(User).filter(User.username == test_username).first()
        if existing_user:
            print(f"   User '{test_username}' already exists, deleting...")
            db.delete(existing_user)
            db.commit()
        
        # Create new user
        new_user = User(
            username=test_username,
            email=test_email,
            password_hash=hashed
        )
        db.add(new_user)
        db.commit()
        db.refresh(new_user)
        print(f"   ✓ User created with ID: {new_user.id}")
        
        # Test authentication
        print("5. Testing authentication...")
        authenticated_user = authenticate_user(db, test_username, test_password)
        if authenticated_user:
            print(f"   ✓ Authentication successful for user: {authenticated_user.username}")
        else:
            print("   ✗ Authentication failed")
            return False
        
        # Test session creation
        print("6. Testing session creation...")
        session_token = create_user_session(db, authenticated_user.id)
        if session_token:
            print(f"   ✓ Session created with token: {session_token[:16]}...")
        else:
            print("   ✗ Session creation failed")
            return False
        
        print("\n✅ All simple authentication tests passed!")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    success = test_simple_auth_system()
    sys.exit(0 if success else 1)
