#!/usr/bin/env python3
"""
Installation script for authentication dependencies
"""

import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Failed to install {package}: {e}")
        return False

def main():
    """Install required packages for authentication"""
    print("Installing authentication dependencies...")
    
    # Core authentication packages
    packages = [
        "bcrypt>=4.0.0",
        "passlib>=1.7.4", 
        "python-jose[cryptography]>=3.3.0",
        "python-decouple>=3.8",
        "sqlalchemy>=2.0.0"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"\nInstalling {package}...")
        if install_package(package):
            print(f"✓ Successfully installed {package}")
        else:
            print(f"✗ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n❌ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually using:")
        for package in failed_packages:
            print(f"  pip install {package}")
        return False
    else:
        print("\n✅ All authentication dependencies installed successfully!")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
