"""
Simple authentication system without external dependencies
For testing purposes when bcrypt/jose are not available
"""

from datetime import datetime, timedelta, timezone
from typing import Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import hashlib
import secrets
import json
import base64
from sqlalchemy.orm import Session
from .database import get_db, User, UserSession
import os

# Security configuration
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")

# HTTP Bearer token scheme
security = HTTPBearer()

def simple_hash_password(password: str) -> str:
    """Simple password hashing using hashlib (for testing only)"""
    salt = secrets.token_hex(16)
    password_hash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt.encode('utf-8'), 100000)
    return f"{salt}:{password_hash.hex()}"

def verify_simple_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against simple hash"""
    try:
        salt, stored_hash = hashed_password.split(':')
        password_hash = hashlib.pbkdf2_hmac('sha256', plain_password.encode('utf-8'), salt.encode('utf-8'), 100000)
        return password_hash.hex() == stored_hash
    except:
        return False

def create_simple_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """Create a simple token (for testing only)"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)
    
    to_encode.update({"exp": expire.timestamp()})
    
    # Simple encoding (NOT secure for production)
    token_data = json.dumps(to_encode)
    encoded = base64.b64encode(token_data.encode()).decode()
    return encoded

def verify_simple_token(token: str) -> Optional[dict]:
    """Verify simple token"""
    try:
        decoded_data = base64.b64decode(token.encode()).decode()
        data = json.loads(decoded_data)
        
        # Check expiration
        if data.get("exp", 0) < datetime.now(timezone.utc).timestamp():
            return None
            
        return data
    except:
        return None

def create_session_token() -> str:
    """Create a secure session token"""
    return secrets.token_urlsafe(32)

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    """Authenticate a user with username and password"""
    user = db.query(User).filter(User.username == username).first()
    if not user:
        return None
    if not verify_simple_password(password, user.password_hash):
        return None
    return user

def create_user_session(db: Session, user_id: int) -> str:
    """Create a new session for a user"""
    session_token = create_session_token()
    
    # Create session record
    db_session = UserSession(
        user_id=user_id,
        session_token=session_token,
        expires_at=datetime.now(timezone.utc) + timedelta(days=7)
    )
    db.add(db_session)
    db.commit()
    db.refresh(db_session)
    
    return session_token

def get_user_from_token(db: Session, token: str) -> Optional[User]:
    """Get user from session token"""
    session = db.query(UserSession).filter(
        UserSession.session_token == token,
        UserSession.is_active == "true",
        UserSession.expires_at > datetime.now(timezone.utc)
    ).first()
    
    if not session:
        return None
    
    return session.user

def invalidate_session(db: Session, token: str) -> bool:
    """Invalidate a session token"""
    session = db.query(UserSession).filter(UserSession.session_token == token).first()
    if session:
        session.is_active = "false"
        db.commit()
        return True
    return False

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """Get the current authenticated user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        token = credentials.credentials
        user = get_user_from_token(db, token)
        if user is None:
            raise credentials_exception
        return user
    except Exception:
        raise credentials_exception

async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False)),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """Get the current user if authenticated, otherwise return None"""
    if not credentials:
        return None
    
    try:
        token = credentials.credentials
        user = get_user_from_token(db, token)
        return user
    except Exception:
        return None

# Alias functions for compatibility
verify_password = verify_simple_password
get_password_hash = simple_hash_password
create_access_token = create_simple_token
